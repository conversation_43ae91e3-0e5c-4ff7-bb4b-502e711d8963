<?php $__env->startSection('title', 'Tryout Programs'); ?>

<style>
    /* Custom Select2 Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-container .select2-selection--single {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        height: 43px;
        user-select: none;
        -webkit-user-select: none;
    }

    .select2-container--default .select2-selection--single {
        background-color: #d9d9d9;
        border-radius: 14px;
        height: 43px;
        border: none;
        padding: 10px 15px;
        font: 500 16px/1 "Montserrat", sans-serif;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #000;
        line-height: 24px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 43px;
        right: 10px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        border-color: #062e69 transparent transparent transparent;
    }

    .select2-dropdown {
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 14px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        padding: 8px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #0b4499;
        color: white;
    }

    .select2-container--default .select2-results__option {
        padding: 8px 12px;
        font-family: "Montserrat", sans-serif;
    }

    .select2-container--default .select2-selection--single .select2-selection__placeholder {
        color: #666;
    }

    /* Fix for Select2 inside Bootstrap modal */
    .modal-open .select2-container--open .select2-dropdown {
        z-index: 1056;
        /* Higher than modal backdrop */
    }

    /* Modal Button Styling */
    #create-team-button {
        margin: 0 auto;
        min-width: 200px;
    }

    .select2-container .select2-selection--single {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        height: 43px !important;
        user-select: none;
        -webkit-user-select: none
    }

    /* Modal Form Styling */
    #team-create-modal .modal-content {
        border-radius: 20px;
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    #team-create-modal .modal-body {
        padding: 2rem;
    }

    #team-create-modal .form-label {
        margin-bottom: 0.875rem;
        color: #c1b05c;
        font-weight: 900;
        letter-spacing: 2px;
    }

    #team-create-modal .heading h2 {
        color: #062e69;
        font-weight: 900;
        letter-spacing: 2px;
    }

    #team-create-modal .invalid-feedback-admin {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Responsive fixes */
    @media (max-width: 767px) {
        #team-create-modal .modal-dialog {
            margin: 0.5rem;
            max-width: calc(100% - 1rem);
        }

        #team-create-modal .modal-body {
            padding: 1.5rem;
        }

        #create-team-button {
            width: 100%;
        }
    }

    /* Spinning animation for loading states */
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>

<?php $__env->startSection('content'); ?>

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Programs</h1>
    </section>

    <?php if(session('success')): ?>
        <div id="successMessageForSession">
            <span id="successText"><?php echo e(session('success')); ?></span>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div id="errorMessageForSession">
            <span id="errorText"><?php echo e(session('error')); ?></span>
        </div>
    <?php endif; ?>



    <section class="sec partition-hr">
        <a class="cta mb-5 ms-5" href="<?php echo e(route('admin.program.allPrograms')); ?>">Back</a>
        

        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>

    <section class="sec program-table">

        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('admin.tryout-programs');

$__html = app('livewire')->mount($__name, $__params, 'lw-2726498298-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    </section>






<div class="modal fade" id="regularInviteModal" tabindex="-1" aria-labelledby="regularInviteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);">
            <div class="modal-header" style="border-bottom: 2px solid #d9d9d9;">
                <h5 class="modal-title navy-text" id="regularInviteModalLabel" style="font: 900 18px/1 'Montserrat', sans-serif;">Invite Player to Program</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                
                <div id="modalLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted" style="font: 500 14px/1 'Montserrat', sans-serif;">Loading available players...</p>
                </div>

                
                <div id="modalContent">
                    <div class="mb-3">
                        <label for="playerSelect" class="form-label text-uppercase" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif; letter-spacing: 1px;">Select Player</label>
                        <select class="form-control" id="playerSelect" style="background-color: #d9d9d9; border-radius: 12px; border: none; padding: 12px 15px; font: 500 16px/1 'Montserrat', sans-serif;">
                            <option value="">Choose a player...</option>
                        </select>
                        <small id="playerSelectHelp" class="text-muted" style="font: 400 12px/1 'Montserrat', sans-serif;">Select a player to invite to this program</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="border-top: 2px solid #d9d9d9; padding: 1.5rem 2rem;">
                <button type="button" class="cta" style="background: #6c757d; border-color: #6c757d; margin-right: 1rem;" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="cta" id="sendInvitationBtn" onclick="sendInvitation()" disabled style="opacity: 0.6; cursor: not-allowed;">
                    <span id="sendBtnText">Send Invitation</span>
                    <span id="sendBtnLoading" style="display: none;">
                        <i class="bi bi-arrow-clockwise spin me-1"></i> Sending...
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    let currentProgramId = null;
    let availablePlayers = [];
    let playerSelect = null;


    function openRegularInviteModal(programId) {
        console.log('Opening modal for program:', programId);
        currentProgramId = programId;

        // Open the modal
        const modal = new bootstrap.Modal(document.getElementById('regularInviteModal'));
        modal.show();

        // Load real players data
        loadAvailablePlayers();
    }

    // Load available players from the server
    function loadAvailablePlayers() {
        // Show loading state
        document.getElementById('modalLoading').style.display = 'block';
        document.getElementById('modalContent').style.display = 'none';

        // Make AJAX request to get available players
        fetch(`/admin/api/available-players-for-tryout/${currentProgramId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    availablePlayers = data.players;
                    initializePlayerSelect();
                } else {
                    console.error('Failed to load players:', data.message);
                    availablePlayers = [];
                    initializePlayerSelect();
                }

                // Hide loading and show content
                document.getElementById('modalLoading').style.display = 'none';
                document.getElementById('modalContent').style.display = 'block';
            })
            .catch(error => {
                console.error('Error loading players:', error);
                availablePlayers = [];
                initializePlayerSelect();

                // Hide loading and show content
                document.getElementById('modalLoading').style.display = 'none';
                document.getElementById('modalContent').style.display = 'block';
            });
    }


    function initializePlayerSelect() {
        const select = document.getElementById('playerSelect');
        const helpText = document.getElementById('playerSelectHelp');

        // Destroy existing Select2 instance if it exists
        if (playerSelect) {
            playerSelect.destroy();
        }

        // Clear and populate select options
        select.innerHTML = '<option value="">Choose a player...</option>';

        if (availablePlayers.length === 0) {
            helpText.textContent = 'All players have either registered or been invited to this program.';
            updateSendButton();
            return;
        }

        // Add player options
        availablePlayers.forEach(player => {
            const option = document.createElement('option');
            option.value = player.id;
            option.textContent = `${player.name} (${player.email})`;
            select.appendChild(option);
        });

        // Initialize Select2 with search functionality
        playerSelect = $('#playerSelect').select2({
            placeholder: 'Choose a player...',
            allowClear: true,
            dropdownParent: $('#regularInviteModal'),
            width: '100%',
            theme: 'default',
            templateResult: function(player) {
                if (!player.id) {
                    return player.text;
                }

                // Find the player data
                const playerData = availablePlayers.find(p => p.id == player.id);
                if (playerData) {
                    return $(`<div><strong>${playerData.name}</strong><br><small class="text-muted">${playerData.email}</small></div>`);
                }
                return player.text;
            },
            templateSelection: function(player) {
                if (!player.id) {
                    return player.text;
                }

                const playerData = availablePlayers.find(p => p.id == player.id);
                if (playerData) {
                    return playerData.name;
                }
                return player.text;
            }
        });

        // Add change event listener
        playerSelect.on('change', function() {
            updateSendButton();
        });

        helpText.textContent = 'Select a player to invite to this program';
        updateSendButton();
    }


    function updateSendButton() {
        const select = document.getElementById('playerSelect');
        const sendBtn = document.getElementById('sendInvitationBtn');

        if (select.value && availablePlayers.length > 0) {
            sendBtn.disabled = false;
            sendBtn.style.opacity = '1';
            sendBtn.style.cursor = 'pointer';
        } else {
            sendBtn.disabled = true;
            sendBtn.style.opacity = '0.6';
            sendBtn.style.cursor = 'not-allowed';
        }
    }


    function sendInvitation() {
        const select = document.getElementById('playerSelect');
        const selectedPlayerId = select.value;

        if (!selectedPlayerId || !currentProgramId) {
            alert('Please select a player');
            return;
        }

        // Show loading state
        document.getElementById('sendBtnText').style.display = 'none';
        document.getElementById('sendBtnLoading').style.display = 'inline';
        document.getElementById('sendInvitationBtn').disabled = true;

        // Prepare data for the request
        const formData = new FormData();
        formData.append('player_id', selectedPlayerId);
        formData.append('program_id', currentProgramId);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        // Send AJAX request
        fetch('/admin/api/send-tryout-program-invitation', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                alert(data.message);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('regularInviteModal'));
                modal.hide();

                // Optionally refresh the page or update the UI
                location.reload();
            } else {
                // Show error message
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error sending invitation:', error);
            alert('An error occurred while sending the invitation. Please try again.');
        })
        .finally(() => {
            // Reset button state
            document.getElementById('sendBtnText').style.display = 'inline';
            document.getElementById('sendBtnLoading').style.display = 'none';
            document.getElementById('sendInvitationBtn').disabled = false;
        });
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        // Reset modal when closed
        document.getElementById('regularInviteModal').addEventListener('hidden.bs.modal', function () {
            currentProgramId = null;
            availablePlayers = [];

            // Destroy Select2 instance if it exists
            if (playerSelect) {
                playerSelect.destroy();
                playerSelect = null;
            }

            // Reset select element
            document.getElementById('playerSelect').innerHTML = '<option value="">Choose a player...</option>';
            updateSendButton();
        });
    });
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Mass-Premier-Courts\resources\views/admin/tryoutPrograms.blade.php ENDPATH**/ ?>